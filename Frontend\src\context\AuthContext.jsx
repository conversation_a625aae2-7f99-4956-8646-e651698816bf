import { createContext, useState, useContext, useEffect, useMemo } from "react";
import authService from "../services/authService.js";

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in and get profile
    const initializeAuth = async () => {
      try {
        if (authService.isAuthenticated()) {
          const response = await authService.getProfile();
          if (response.success) {
            setCurrentUser(response.data.user);
          } else {
            // Token is invalid, remove it
            authService.logout();
          }
        }
      } catch (error) {
        console.error("Auth initialization error:", error);
        authService.logout();
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials) => {
    try {
      setLoading(true);
      const response = await authService.login(credentials);

      if (response.success) {
        setCurrentUser(response.data.user);
        return { success: true, user: response.data.user };
      } else {
        return { success: false, message: response.message };
      }
    } catch (error) {
      console.error("Login error:", error);
      return { success: false, message: error.message || "Login failed" };
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData) => {
    console.log("at register functoin : ", userData);
    console.log("this test purpose register functoin : ", userData);
    try {
      setLoading(true);
      const response = await authService.register(userData);

      if (response.success) {
        setCurrentUser(response.data.user);
        return { success: true, user: response.data.user };
      } else {
        return { success: false, message: response.message };
      }
    } catch (error) {
      console.error("Registration error:", error);
      return {
        success: false,
        message: error.message || "Registration failed",
      };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await authService.logout();
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      setCurrentUser(null);
    }
  };

  const updateProfile = async (profileData) => {
    try {
      // This will be handled by userService, but we update the context here
      setCurrentUser((prev) => ({ ...prev, ...profileData }));
    } catch (error) {
      console.error("Update profile error:", error);
    }
  };

  const isAdmin = useMemo(() => {
    return currentUser?.is_admin === true;
  }, [currentUser?.is_admin]);

  const isAuthenticated = useMemo(() => {
    return !!currentUser && authService.isAuthenticated();
  }, [currentUser]);

  const value = {
    currentUser,
    login,
    register,
    logout,
    updateProfile,
    isAdmin,
    isAuthenticated,
    loading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  return useContext(AuthContext);
};

export default AuthContext;
